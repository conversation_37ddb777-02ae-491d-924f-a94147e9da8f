const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../config/database');
const { asyncHandler } = require('../middleware/errorHandler');

// Validation middleware for faculty login
const validateFacultyLogin = (req, res, next) => {
  const { facultyId, password } = req.body;

  if (!facultyId || !password) {
    return res.status(400).json({
      success: false,
      error: '❌ Faculty ID and password are required'
    });
  }

  // Validate faculty ID format (YYYY-NNNNN)
  const facultyIdPattern = /^[0-9]{4}-[0-9]{5}$/;
  if (!facultyIdPattern.test(facultyId)) {
    return res.status(400).json({
      success: false,
      error: '❌ Invalid faculty ID format. Expected format: YYYY-NNNNN'
    });
  }

  next();
};

// POST /login-faculty - Faculty login
router.post('/login-faculty', validateFacultyLogin, asyncHandler(async (req, res) => {
  const { facultyId, password } = req.body;

  console.log('🚀 Faculty login attempt for:', facultyId);

  // Extract numeric part from formatted faculty ID (YYYY-NNNNN)
  // For faculty, we'll use the last 5 digits as the FacultyID
  const numericPart = facultyId.replace('-', ''); // Remove dash: "2000-00001" -> "*********"
  const facultyIdNumber = parseInt(numericPart.slice(-5)); // Get last 5 digits: "00001" -> 1

  console.log('🔍 Looking for faculty with ID:', facultyIdNumber);

  const selectQuery = `SELECT * FROM faculty WHERE FacultyID = ? AND Status = 'Active'`;
  const [results] = await db.execute(selectQuery, [facultyIdNumber]);

  if (results.length === 0) {
    console.log('❌ Faculty not found or account not active:', facultyId);
    return res.status(401).json({
      success: false,
      error: '❌ Invalid faculty ID or password'
    });
  }

  const faculty = results[0];
  console.log('🔍 Found faculty:', faculty.FullName);

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, faculty.Password);
  if (!isPasswordValid) {
    console.log('❌ Invalid password for faculty:', facultyId);
    return res.status(401).json({
      success: false,
      error: '❌ Invalid faculty ID or password'
    });
  }

  // Remove password from response
  delete faculty.Password;

  // Add the formatted faculty ID to the response for frontend use
  faculty.FormattedFacultyID = facultyId;

  console.log('✅ Faculty login successful:', faculty.FullName);

  res.json({
    success: true,
    message: '✅ Faculty login successful',
    data: faculty
  });
}));

// GET /get-faculty/:facultyID
router.get('/get-faculty/:facultyID', asyncHandler(async (req, res) => {
  const { facultyID } = req.params;

  const selectQuery = `SELECT * FROM faculty WHERE FacultyID = ?`;
  const [results] = await db.execute(selectQuery, [facultyID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Faculty not found.'
    });
  }

  const faculty = results[0];
  delete faculty.Password;

  res.json({
    success: true,
    message: '✅ Faculty found',
    data: faculty
  });
}));

// POST /register-faculty
router.post('/register-faculty', asyncHandler(async (req, res) => {
  const {
    fullName,
    email,
    phoneNumber,
    password,
    department,
    position,
    status = 'Active'
  } = req.body;

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  const insertQuery = `
    INSERT INTO faculty (
      FullName, Email, PhoneNumber, Password, Department, Position, Status
    ) VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  const [result] = await db.execute(
    insertQuery,
    [fullName, email, phoneNumber, hashedPassword, department, position, status]
  );

  console.log('✅ Faculty registered successfully:', fullName);

  res.status(201).json({
    success: true,
    message: '✅ Faculty registered successfully',
    data: {
      facultyId: result.insertId,
      fullName,
      email,
      department,
      position,
      status
    }
  });
}));

module.exports = router;
